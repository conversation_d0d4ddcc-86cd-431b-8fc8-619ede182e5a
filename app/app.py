from fastapi import FastAPI
import uvicorn

# from v1.api import (
#     v1_login_router,
#     v1_schedule_router,
#     v1_rep_schedule_router,
#     v1_site_routers,
#     v1_patient_router,
#     v1_clinical_coord_router,
#     v1_ambra_router,
#     v1_rep_laao_procedure_router,
#     v1_truplan_router,
#     v1_case_summary_router,
#     v1_task_router,
# )
from v2.api import (
    v2_login_router,
    v2_schedule_router,
    v2_rep_schedule_router,
    v2_procedure_detail_router,
    v2_case_router,
    v2_truplan_router,
    v2_ambra_router,
    v2_patient_router,
    v2_clinical_coord_router,
    v2_task_router,
    v2_post_op_router,
    v2_afib_router,
    v2_user_router,
    v2_referring_provider_router,
    v2_pcp_provider_router,
    v2_procedure_type,
    v2_create_patient,
    v2_abstractor_router,
    v2_superset_router,
    v2_keycloak_router,
    v2_patient_import_router,
    v2_jira_router,
    v2_db_logs_router,
    v2_config_router,
    v2_site_creation,
    v2_submission,
    v2_version_router,
    v2_npi_provider_router,
)
from config import settings
from fastapi.middleware.cors import CORSMiddleware
from v1.db.connection import Base, create_engine
from v2.utils import EXCEPTION_HANDLERS
from contextlib import asynccontextmanager
from pymongo import MongoClient


@asynccontextmanager
async def app_lifespan(app: FastAPI):
    app.mongodb_client = MongoClient(settings.DATABASE_URL)
    app.database = app.mongodb_client[settings.DATABASE]
    try:
        yield
    finally:
        app.mongodb_client.close()


app = FastAPI(
    lifespan=app_lifespan,
    title="Cormetrix API Documentation",
    description="Cormetrix API's",
    swagger_ui_parameters={"defaultModelsExpandDepth": -1},
    version="0.1",
    debug=settings.DEBUG,
    exception_handlers=EXCEPTION_HANDLERS,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# API version V2

app.include_router(v2_login_router, prefix="/api/v2")
app.include_router(v2_schedule_router, prefix="/api/v2")
app.include_router(v2_rep_schedule_router, prefix="/api/v2")
app.include_router(v2_procedure_detail_router, prefix="/api/v2")
app.include_router(v2_case_router, prefix="/api/v2")
app.include_router(v2_truplan_router, prefix="/api/v2")
app.include_router(v2_ambra_router, prefix="/api/v2")
app.include_router(v2_patient_router, prefix="/api/v2")
app.include_router(v2_afib_router, prefix="/api/v2")
app.include_router(v2_clinical_coord_router, prefix="/api/v2")
app.include_router(v2_task_router, prefix="/api/v2")
app.include_router(v2_post_op_router, prefix="/api/v2")
app.include_router(v2_user_router, prefix="/api/v2")
app.include_router(v2_referring_provider_router, prefix="/api/v2")
app.include_router(v2_pcp_provider_router, prefix="/api/v2")
app.include_router(v2_procedure_type, prefix="/api/v2")
app.include_router(v2_create_patient, prefix="/api/v2")
app.include_router(v2_abstractor_router, prefix="/api/v2")
app.include_router(v2_superset_router, prefix="/api/v2")
app.include_router(v2_keycloak_router, prefix="/api/v2")
app.include_router(v2_patient_import_router, prefix="/api/v2")
app.include_router(v2_jira_router, prefix="/api/v2")
app.include_router(v2_db_logs_router, prefix="/api/v2")
app.include_router(v2_config_router, prefix="/api/v2")
app.include_router(v2_site_creation, prefix="/api/v2")
app.include_router(v2_submission, prefix="/api/v2")
app.include_router(v2_version_router, prefix="/api/v2")
app.include_router(v2_npi_provider_router, prefix="/api/v2")
if __name__ == "__main__":
    uvicorn.run("app:app", host="0.0.0.0", port=8000, workers=4, reload=True)
