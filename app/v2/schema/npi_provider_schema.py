from pydantic import BaseModel, field_validator
from typing import Optional, List


class NPIProviderSchema(BaseModel):
    """Schema for NPI provider data that maps to our provider creation fields"""

    first_name: Optional[str] = None
    last_name: Optional[str] = None
    middle_name: Optional[str] = None
    credential: Optional[str] = None
    npi_number: str
    email_id: Optional[str] = None
    phone_number: Optional[str] = None
    fax_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    hospital_system: Optional[str] = None
    # Additional fields from NPI that might be useful
    enumeration_type: Optional[str] = None
    enumeration_date: Optional[str] = None
    status: Optional[str] = None
    taxonomies: Optional[List[dict]] = None


class NPILookupRequest(BaseModel):
    npi_number: str

    @field_validator("npi_number")
    def validate_npi_number(cls, v):
        # Remove any spaces or special characters
        npi_clean = "".join(filter(str.isdigit, v))

        # Check if it's exactly 10 digits
        if len(npi_clean) != 10:
            raise ValueError("NPI number must be exactly 10 digits")

        return npi_clean
