from fastapi import APIRouter, Response, status, Request, Depends, HTTPException
from v2.utils import authorizer, api_response
from v2.schema import NPIProviderSchema, NPILookupRequest, ResponseDictSchema
import httpx

router = APIRouter(tags=["V2 NPI Provider"], prefix="/npi-provider")

NPI_REGISTRY_BASE_URL = "https://npiregistry.cms.hhs.gov/api/"


def map_npi_response_to_provider_schema(npi_data: dict) -> dict:
    """
    Maps NPI registry response to our provider schema fields
    """
    result = npi_data.get("results", [])
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Provider not found in NPI registry",
        )

    provider_data = result[0]  # Take the first result
    basic_info = provider_data.get("basic", {})
    addresses = provider_data.get("addresses", [])

    # Find the primary address (LOCATION type preferred, otherwise first available)
    primary_address = None
    mailing_address = None

    for addr in addresses:
        if addr.get("address_purpose") == "LOCATION":
            primary_address = addr
        elif addr.get("address_purpose") == "MAILING":
            mailing_address = addr

    # Use location address if available, otherwise mailing address
    address_to_use = primary_address or mailing_address

    # Extract phone number (remove formatting)
    phone_number = ""
    if address_to_use and address_to_use.get("telephone_number"):
        phone_number = (
            address_to_use["telephone_number"]
            .replace("-", "")
            .replace("(", "")
            .replace(")", "")
            .replace(" ", "")
        )

    # Extract fax number (remove formatting)
    fax_number = ""
    if address_to_use and address_to_use.get("fax_number"):
        fax_number = (
            address_to_use["fax_number"]
            .replace("-", "")
            .replace("(", "")
            .replace(")", "")
            .replace(" ", "")
        )

    # Map the data to our provider schema format (direct fields without wrapper)
    mapped_data = {
        "first_name": basic_info.get("first_name", ""),
        "last_name": basic_info.get("last_name", ""),
        "middle_name": basic_info.get("middle_name", ""),
        "credential": basic_info.get("credential", ""),
        "npi_number": provider_data.get("number", ""),
        "email_id": "",  # NPI registry doesn't provide email
        "phone_number": phone_number,
        "fax_number": fax_number,
        "address": address_to_use.get("address_1", "") if address_to_use else "",
        "city": address_to_use.get("city", "") if address_to_use else "",
        "state": address_to_use.get("state", "") if address_to_use else "",
        "zip_code": address_to_use.get("postal_code", "") if address_to_use else "",
        "hospital_system": "",  # NPI registry doesn't provide this
    }

    return mapped_data


@router.post(
    "/lookup/{npi_number}", response_model=ResponseDictSchema[NPIProviderSchema]
)
async def lookup_provider_by_npi(
    npi_number: str,
    request: Request,
    response: Response,
    token_data: dict = Depends(authorizer),
):
    """
    Lookup provider details from NPI registry by NPI number.
    Returns only the fields that are used for creating referring/PCP providers.
    """
    try:
        # Validate NPI number format
        lookup_request = NPILookupRequest(npi_number=npi_number)
        clean_npi = lookup_request.npi_number

        # Make request to NPI registry
        async with httpx.AsyncClient(timeout=30.0) as client:
            npi_response = await client.get(
                NPI_REGISTRY_BASE_URL, params={"version": "2.1", "number": clean_npi}
            )

            if npi_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="NPI registry service is currently unavailable",
                )

            npi_data = npi_response.json()

            # Check if any results were found
            if npi_data.get("result_count", 0) == 0:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"No provider found with NPI number: {clean_npi}",
                )

            # Map the response to our schema and return with standard format
            provider_data = map_npi_response_to_provider_schema(npi_data)

            response.status_code = status.HTTP_200_OK
            return api_response(
                provider_data,
                f"Provider details fetched successfully for NPI: {clean_npi}",
                "success",
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching provider data: {str(e)}",
        )
