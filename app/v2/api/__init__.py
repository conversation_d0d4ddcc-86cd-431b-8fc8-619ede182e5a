from .login_api import router as v2_login_router
from .patient_api import router as v2_patient_router
from .case_summary_api import router as v2_case_router
from .scheduler_api import router as v2_schedule_router
from .rep_schedules_api import router as v2_rep_schedule_router
from .rep_laao_procedure_api import router as v2_procedure_detail_router
from .truplan_api import router as v2_truplan_router
from .ambra_api import router as v2_ambra_router
from .clinical_coordinator_schedules_api import router as v2_clinical_coord_router
from .task_api import router as v2_task_router
from .post_op_api import router as v2_post_op_router
from .afib_ablation import router as v2_afib_router
from .user_api import router as v2_user_router
from .referring_provider import router as v2_referring_provider_router
from .pcp_provider import router as v2_pcp_provider_router
from .procedure_type import router as v2_procedure_type
from .create_patient_json import router as v2_create_patient
from .abstractor_api import router as v2_abstractor_router
from .superset_api import router as v2_superset_router
from .key_cloak_api import router as v2_keycloak_router
from .patient_import_api import router as v2_patient_import_router
from .jira_api import router as v2_jira_router
from .db_logger_api import router as v2_db_logs_router
from .etl_json_api import router as v2_config_router
from .site_creation_api import router as v2_site_creation
from .ncdr_registry_api import router as v2_submission
from .version_api import router as v2_version_router
from .npi_provider import router as v2_npi_provider_router
